✓ Compiled successfully in 36.0s

Failed to compile.

./src/app/api/stripe/webhook/route.ts
626:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
662:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
665:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
704:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/business/[username]/page.tsx
61:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/campaigns/[id]/edit/page.tsx
67:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
122:72  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/biznis/applications/page.tsx
17:1  Error: Delete `⏎`  prettier/prettier

./src/app/dashboard/biznis/applications/[id]/page.tsx
70:44  Error: Replace `·useState<JobCompletionWithDetails·|·null>(⏎····null⏎··` with `⏎····useState<JobCompletionWithDetails·|·null>(null`  prettier/prettier
114:15  Error: Replace `paymentData·&&·typeof·paymentData·===·'object'·&&·` with `⏎············paymentData·&&⏎············typeof·paymentData·===·'object'·&&`  prettier/prettier
115:1  Error: Replace `··············'total_paid'·in·paymentData·&&·'platform_fee'·in·paymentData·&&·'payment_amount'·in·paymentData` with `············'total_paid'·in·paymentData·&&⏎············'platform_fee'·in·paymentData·&&⏎············'payment_amount'·in·paymentData⏎··········`  prettier/prettier
116:28  Error: Replace `paymentData·as·{·total_paid:·number;·platform_fee:·number;·payment_amount:·number;·}` with `⏎··············paymentData·as·{⏎················total_paid:·number;⏎················platform_fee:·number;⏎················payment_amount:·number;⏎··············}⏎············`  prettier/prettier

./src/app/dashboard/biznis/offers/page.tsx
66:19  Error: Insert `⏎··········`  prettier/prettier
67:11  Error: Insert `··`  prettier/prettier
68:1  Error: Replace `··········offer_type:·(offer.offer_type·as·'custom'·|·'package_order')·||·'custom'` with `············offer_type:⏎··············(offer.offer_type·as·'custom'·|·'package_order')·||·'custom',`  prettier/prettier
69:1  Error: Replace `········}))` with `··········}))⏎········`  prettier/prettier
162:32  Error: Replace `·offer.updated_at·||·offer.created_at·||` with `⏎······················offer.updated_at·||⏎······················offer.created_at·||⏎·····················`  prettier/prettier   

./src/app/dashboard/biznis/offers/[id]/page.tsx
54:1  Error: Delete `⏎`  prettier/prettier
788:30  Error: Replace `JSON.parse(jobCompletion.submission_files·as·string).length` with `⏎······························JSON.parse(⏎································jobCompletion.submission_files·as·string⏎······························).length⏎····························`  prettier/prettier

./src/app/dashboard/biznis/packages/page.tsx
58:23  Error: Replace `userSub` with `⏎········userSub⏎·········`  prettier/prettier
59:1  Error: Insert `······`  prettier/prettier
60:1  Error: Insert `······`  prettier/prettier
61:9  Error: Replace `plan_name:·undefined` with `······plan_name:·undefined,`  prettier/prettier
62:7  Error: Replace `}·:·null` with `······}⏎··········:·null⏎······`  prettier/prettier

./src/app/dashboard/biznis/profile/page.tsx
164:38  Error: Replace `(cat)` with `cat`  prettier/prettier

./src/app/dashboard/campaigns/page.tsx
226:6  Warning: React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/dashboard/campaigns/test/page.tsx
231:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/chat/permissions/page.tsx
20:6  Warning: React Hook useEffect has a missing dependency: 'loadPermissions'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
24:1  Error: Delete `····`  prettier/prettier

./src/app/dashboard/influencer/applications/[id]/page.tsx
115:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
514:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
518:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
571:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/offers/page.tsx
164:69  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/offers/[id]/page.tsx
769:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
780:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/packages/page.tsx
32:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
62:6  Warning: React Hook useEffect has a missing dependency: 'loadInfluencer'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
100:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/page.tsx
13:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/pricing/page.tsx
108:20  Error: Replace `platformsData·as·Platform[]` with `(platformsData·as·Platform[])`  prettier/prettier
118:23  Error: Replace `contentTypesData·as·ContentType[]` with `(contentTypesData·as·ContentType[])`  prettier/prettier
487:49  Error: Replace `pkg.id,·pkg.auto_generated_name·||·''` with `⏎······························pkg.id,⏎······························pkg.auto_generated_name·||·''⏎····························`  prettier/prettier

./src/app/dashboard/influencer/profile/page.tsx
70:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
71:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
129:6  Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadData'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/dashboard/notifications/page.tsx
312:26  Error: Replace `(value)·=>·setActiveTab(value·as·'all'·|·'unread'·|·'read')` with `value·=>⏎············setActiveTab(value·as·'all'·|·'unread'·|·'read')⏎··········`  prettier/prettier

./src/app/dashboard/page.tsx
22:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/influencer/[username]/InfluencerProfileClient.tsx
62:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
83:6  Warning: React Hook useEffect has a missing dependency: 'loadBusinessProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
193:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/influencer/[username]/page.tsx
62:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/marketplace/campaigns/page.tsx
8:10  Error: 'Separator' is defined but never used.  @typescript-eslint/no-unused-vars
9:19  Error: 'Star' is defined but never used.  @typescript-eslint/no-unused-vars
53:10  Error: 'featuredCampaigns' is assigned a value but never used.  @typescript-eslint/no-unused-vars
53:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
54:10  Error: 'loadingFeatured' is assigned a value but never used.  @typescript-eslint/no-unused-vars
126:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/marketplace/influencers/page.tsx
132:12  Error: Replace `(error·&&·!isLoading)` with `error·&&·!isLoading`  prettier/prettier

./src/app/prijava/page.tsx
9:15  Error: 'AuthResponse' is defined but never used.  @typescript-eslint/no-unused-vars
55:41  Error: Replace `await·signIn(data.email,·data.password)·as·{·data:·any,` with `(await·signIn(⏎········data.email,⏎········data.password⏎······))·as·{·data:·any;`  prettier/prettier
55:92  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
55:104  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profil/edit/page.tsx
60:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
99:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
105:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
115:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
123:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
125:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
126:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
137:16  Error: Replace `'gender',·(influencerData.profiles?.gender·as·"male"·|·"female"·|·"other"·|·"prefer_not_to_say"·|·undefined)·||·undefined` with `⏎········'gender',⏎········(influencerData.profiles?.gender·as⏎··········|·'male'⏎··········|·'female'⏎··········|·'other'⏎··········|·'prefer_not_to_say'⏎··········|·undefined)·||·undefined⏎······`  prettier/prettier

./src/app/profil/kreiranje/biznis/onboarding/page.tsx
127:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
366:54  Error: Replace `⏎········.from('businesses')⏎········` with `.from('businesses')`  prettier/prettier
369:9  Error: Delete `··`  prettier/prettier
370:1  Error: Delete `··`  prettier/prettier
371:1  Error: Replace `············` with `··········`  prettier/prettier
372:1  Error: Delete `··`  prettier/prettier
373:1  Error: Replace `··········` with `········`  prettier/prettier
374:9  Error: Delete `··`  prettier/prettier
375:1  Error: Delete `··`  prettier/prettier
376:9  Error: Delete `··`  prettier/prettier
377:1  Error: Delete `··`  prettier/prettier

./src/app/profil/kreiranje/influencer/onboarding/page.tsx
199:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
373:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
382:36  Error: Insert `⏎·····`  prettier/prettier
393:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/page.tsx
20:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/registracija/business/page.tsx
81:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/registracija/influencer/page.tsx
73:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/campaigns/BusinessOfferCard.tsx
83:22  Error: Replace `offer.influencer·?·getDisplayName(offer.influencer)·:·'Unknown'` with `⏎··················offer.influencer⏎····················?·getDisplayName(offer.influencer)⏎····················:·'Unknown'⏎················`  prettier/prettier
86:34  Error: Replace `·?·getInitials(getDisplayName(offer.influencer))` with `⏎··················?·getInitials(getDisplayName(offer.influencer))⏎·················`  prettier/prettier
91:34  Error: Replace `·?·getDisplayName(offer.influencer)` with `⏎··················?·getDisplayName(offer.influencer)⏎·················`  prettier/prettier

./src/components/campaigns/campaign-filters.tsx
87:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
345:75  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/campaigns/create-campaign-form.tsx
45:19  Error: Replace `.array(z.enum(['post',·'story',·'reel',·'video',·'blog']))` with `⏎····.array(z.enum(['post',·'story',·'reel',·'video',·'blog']))⏎····`  prettier/prettier
135:3  Error: 'onCancel' is defined but never used.  @typescript-eslint/no-unused-vars
136:3  Error: 'onSaveDraft' is defined but never used.  @typescript-eslint/no-unused-vars
140:3  Error: 'isEditing' is defined but never used.  @typescript-eslint/no-unused-vars
215:35  Error: Insert `⏎·········`  prettier/prettier
402:51  Error: Replace `·?·allContentTypes` with `⏎··········?·allContentTypes⏎·········`  prettier/prettier
479:16  Error: Delete `·`  prettier/prettier
493:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
844:73  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/chat/ChatEnableButton.tsx
41:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/chat/ChatRoom.tsx
42:20  Error: 'setIsTyping' is assigned a value but never used.  @typescript-eslint/no-unused-vars
166:29  Error: Insert `⏎·········`  prettier/prettier

./src/components/dashboard/DashboardLayout.tsx
25:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/marketplace/filters.tsx
437:54  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/marketplace/horizontal-filters.tsx
84:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/navigation/DesktopHeader.tsx
36:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
42:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
48:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/navigation/MobileTopNavbar.tsx
48:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
172:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/navigation/ResponsiveNavigation.tsx
8:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/notifications/NotificationDropdown.tsx
78:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/offers/DirectOfferForm.tsx
128:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
130:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/onboarding/PackageStep.tsx
109:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
110:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
129:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/onboarding/steps/PackageCreationStep.tsx
100:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
106:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/reviews/ReviewStats.tsx
94:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/category-grid-selector.tsx
52:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/category-selector.tsx
68:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/image-cropper.tsx
200:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ui/infinite-scroll.tsx
167:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/platform-selector.tsx
108:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/pricing-matrix.tsx
98:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
99:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/table.tsx
1:24  Error: Replace `"react"` with `'react';`  prettier/prettier
2:20  Error: Replace `"@/lib/utils"` with `'@/lib/utils';`  prettier/prettier
11:21  Error: Replace `"w-full·caption-bottom·text-sm"` with `'w-full·caption-bottom·text-sm'`  prettier/prettier
15:3  Error: Insert `;`  prettier/prettier
16:21  Error: Replace `"Table"` with `'Table';`  prettier/prettier
22:34  Error: Replace `"[&_tr]:border-b"` with `'[&_tr]:border-b'`  prettier/prettier
23:3  Error: Insert `;`  prettier/prettier
24:27  Error: Replace `"TableHeader"` with `'TableHeader';`  prettier/prettier
32:19  Error: Replace `"[&_tr:last-child]:border-0"` with `'[&_tr:last-child]:border-0'`  prettier/prettier
35:3  Error: Insert `;`  prettier/prettier
36:25  Error: Replace `"TableBody"` with `'TableBody';`  prettier/prettier
45:7  Error: Replace `"border-t·bg-muted/50·font-medium·[&>tr]:last:border-b-0"` with `'border-t·bg-muted/50·font-medium·[&>tr]:last:border-b-0'`  prettier/prettier
50:3  Error: Insert `;`  prettier/prettier
51:27  Error: Replace `"TableFooter"` with `'TableFooter';`  prettier/prettier
60:7  Error: Replace `"border-b·transition-colors·hover:bg-muted/50·data-[state=selected]:bg-muted"` with `'border-b·transition-colors·hover:bg-muted/50·data-[state=selected]:bg-muted'`  prettier/prettier
65:3  Error: Insert `;`  prettier/prettier
66:24  Error: Replace `"TableRow"` with `'TableRow';`  prettier/prettier
75:7  Error: Replace `"h-12·px-4·text-left·align-middle·font-medium·text-muted-foreground·[&:has([role=checkbox])]:pr-0"` with `'h-12·px-4·text-left·align-middle·font-medium·text-muted-foreground·[&:has([role=checkbox])]:pr-0'`  prettier/prettier
80:3  Error: Insert `;`  prettier/prettier
81:25  Error: Replace `"TableHead"` with `'TableHead';`  prettier/prettier
89:19  Error: Replace `"p-4·align-middle·[&:has([role=checkbox])]:pr-0"` with `'p-4·align-middle·[&:has([role=checkbox])]:pr-0'`  prettier/prettier
92:3  Error: Insert `;`  prettier/prettier
93:25  Error: Replace `"TableCell"` with `'TableCell';`  prettier/prettier
101:19  Error: Replace `"mt-4·text-sm·text-muted-foreground"` with `'mt-4·text-sm·text-muted-foreground'`  prettier/prettier
104:3  Error: Insert `;`  prettier/prettier
105:28  Error: Replace `"TableCaption"` with `'TableCaption';`  prettier/prettier
116:2  Error: Insert `;⏎`  prettier/prettier

./src/hooks/useInfiniteScroll.ts
98:6  Warning: React Hook useCallback has a missing dependency: 'isLoading'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
136:6  Warning: React Hook useCallback has a missing dependency: 'isLoading'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/lib/api-auth.ts
326:7  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/api-middleware.ts
242:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/campaigns.ts
195:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
559:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
560:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
565:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
566:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
568:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
569:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
571:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
572:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
574:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
575:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
581:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
582:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
585:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
586:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
600:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
601:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
610:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
622:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
623:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
632:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
922:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
970:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1220:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1421:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1434:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1443:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1444:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1631:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1801:51  Error: Replace `⏎········data·as·any[]` with `data·as·any[]).map(`  prettier/prettier
1802:17  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1803:7  Error: Replace `).map(` with `··`  prettier/prettier
1803:19  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1804:1  Error: Insert `··`  prettier/prettier
1805:1  Error: Replace `········` with `··········`  prettier/prettier
1806:1  Error: Insert `··`  prettier/prettier
1807:9  Error: Insert `··`  prettier/prettier
1808:1  Error: Insert `··`  prettier/prettier
1809:9  Error: Insert `··`  prettier/prettier
1810:1  Error: Insert `··`  prettier/prettier
1811:9  Error: Insert `··`  prettier/prettier
1812:1  Error: Insert `··`  prettier/prettier
1813:9  Error: Insert `··`  prettier/prettier
1814:1  Error: Replace `········` with `··········`  prettier/prettier
1815:1  Error: Insert `··`  prettier/prettier
1816:9  Error: Insert `··`  prettier/prettier
1817:7  Error: Replace `})` with `··})⏎······`  prettier/prettier
1823:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1823:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/startup-config.ts
127:34  Error: 'env' is defined but never used.  @typescript-eslint/no-unused-vars

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules