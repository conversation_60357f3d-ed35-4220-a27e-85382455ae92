✓ Compiled successfully in 28.0s

Failed to compile.

./src/app/api/stripe/webhook/route.ts
626:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
662:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
665:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
704:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/business/[username]/page.tsx
61:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/campaigns/[id]/edit/page.tsx
67:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
122:72  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/campaigns/page.tsx
226:6  Warning: React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/dashboard/campaigns/test/page.tsx
231:40  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/chat/permissions/page.tsx
20:6  Warning: React Hook useEffect has a missing dependency: 'loadPermissions'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/dashboard/influencer/applications/[id]/page.tsx
115:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
514:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
518:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
571:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/offers/page.tsx
164:69  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/offers/[id]/page.tsx
769:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
780:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/packages/page.tsx
32:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
62:6  Warning: React Hook useEffect has a missing dependency: 'loadInfluencer'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
100:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/page.tsx
13:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/dashboard/influencer/profile/page.tsx
70:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
71:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
129:6  Warning: React Hook useEffect has missing dependencies: 'checkSubscription' and 'loadData'. Either include them or remove the dependency array.  react-hooks/exhaustive-deps

./src/app/dashboard/page.tsx
22:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/influencer/[username]/InfluencerProfileClient.tsx
62:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
63:58  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
83:6  Warning: React Hook useEffect has a missing dependency: 'loadBusinessProfile'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
193:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/influencer/[username]/page.tsx
62:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/marketplace/campaigns/page.tsx
53:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
126:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/prijava/page.tsx
9:15  Error: 'AuthResponse' is defined but never used.  @typescript-eslint/no-unused-vars
58:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
58:33  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profil/edit/page.tsx
60:62  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
61:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
99:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
105:39  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
115:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
123:36  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
125:27  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
126:24  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/biznis/onboarding/page.tsx
127:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/influencer/onboarding/page.tsx
199:25  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
373:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
394:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/profil/kreiranje/page.tsx
20:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/registracija/business/page.tsx
81:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/app/registracija/influencer/page.tsx
73:21  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/campaigns/campaign-filters.tsx
87:66  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
345:75  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/campaigns/create-campaign-form.tsx
498:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
863:73  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/chat/ChatEnableButton.tsx
41:48  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/chat/ChatRoom.tsx
42:20  Error: 'setIsTyping' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/dashboard/DashboardLayout.tsx
25:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/marketplace/filters.tsx
437:54  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/marketplace/horizontal-filters.tsx
84:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/navigation/DesktopHeader.tsx
36:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
42:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
48:9  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/navigation/MobileTopNavbar.tsx
48:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
172:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/navigation/ResponsiveNavigation.tsx
8:12  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/notifications/NotificationDropdown.tsx
78:50  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/offers/DirectOfferForm.tsx
128:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
130:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/onboarding/PackageStep.tsx
109:45  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
110:51  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
129:28  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/onboarding/steps/PackageCreationStep.tsx
100:47  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
106:53  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/reviews/ReviewStats.tsx
94:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/category-grid-selector.tsx
52:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/category-selector.tsx
68:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/image-cropper.tsx
200:15  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/ui/infinite-scroll.tsx
167:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/platform-selector.tsx
108:29  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/ui/pricing-matrix.tsx
98:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
99:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/hooks/useInfiniteScroll.ts
98:6  Warning: React Hook useCallback has a missing dependency: 'isLoading'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps
136:6  Warning: React Hook useCallback has a missing dependency: 'isLoading'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/lib/api-auth.ts
326:7  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/api-middleware.ts
242:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/campaigns.ts
195:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
559:37  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
560:30  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
565:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
566:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
568:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
569:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
571:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
572:32  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
574:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
575:34  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
581:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
582:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
585:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
586:26  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
600:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
601:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
610:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
622:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
623:22  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
632:46  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
922:42  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
970:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1220:44  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1421:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1434:20  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1443:18  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1444:16  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1631:38  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1801:59  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1802:15  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1823:35  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
1823:52  Warning: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/startup-config.ts
127:34  Error: 'env' is defined but never used.  @typescript-eslint/no-unused-vars