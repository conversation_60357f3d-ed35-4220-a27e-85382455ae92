'use client';

import { useCallback, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import {
  searchInfluencers,
  type InfluencerSearchResult,
} from '@/lib/marketplace';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { InfluencerCard } from '@/components/marketplace/InfluencerCard';
import {
  InfiniteScroll,
  InfiniteScrollCache,
} from '@/components/ui/infinite-scroll';
import { useInfiniteScroll, useCachedFilters } from '@/hooks/useInfiniteScroll';

const CACHE_KEY = 'marketplace-influencers';
const ITEMS_PER_PAGE = 20;

export default function InfluencerMarketplacePage() {
  // Cache-irani filteri (za kasnije kad dodamo filter UI)
  const [filters] = useCachedFilters(CACHE_KEY, {});

  // Smart cache management - clear cache unless coming back from influencer profile
  useEffect(() => {
    // Simple approach: check if we have cached data and if last visited was influencer profile
    const lastVisitedPage = sessionStorage.getItem('lastVisitedPage');
    const cachedData = sessionStorage.getItem(`data-${CACHE_KEY}`);
    const isComingFromInfluencerProfile =
      lastVisitedPage?.startsWith('/influencer/');

    if (cachedData && isComingFromInfluencerProfile) {
      // Keep cache if we have data and coming from influencer profile
      // Cache will be restored automatically by useInfiniteScroll
    } else if (cachedData && !isComingFromInfluencerProfile) {
      // Clear cache if we have data but not coming from influencer profile
      InfiniteScrollCache.clearCache(CACHE_KEY);
    }

    // Set current page as last visited (only once)
    setTimeout(() => {
      sessionStorage.setItem('lastVisitedPage', '/marketplace/influencers');
    }, 100);

    // Global navigation tracking - track when user navigates away
    const handleBeforeUnload = () => {
      // This will be called when user navigates away or refreshes
      const currentPath = window.location.pathname;
      if (currentPath === '/marketplace/influencers') {
        sessionStorage.setItem('lastVisitedPage', '/marketplace/influencers');

        // Also save current scroll position
        const scrollTop = window.scrollY || window.pageYOffset;
        sessionStorage.setItem(`scroll-${CACHE_KEY}`, scrollTop.toString());
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Funkcija za učitavanje podataka
  const fetchInfluencers = useCallback(
    async (offset: number, limit: number) => {
      try {
        const { data, error } = await searchInfluencers({
          ...filters,
          offset,
          limit,
          sortBy: 'newest',
        });

        if (error) {
          console.error('Error loading influencers:', error);
          throw error;
        }

        const influencers = data || [];
        const hasMore = influencers.length === limit; // Ako je broj resultata = limit, ima još

        return {
          data: influencers,
          hasMore,
          error: null,
        };
      } catch (error) {
        return {
          data: [],
          hasMore: false,
          error,
        };
      }
    },
    [filters]
  );

  // Infinite scroll hook
  const {
    data: influencers,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    actions,
  } = useInfiniteScroll<InfluencerSearchResult>({
    fetchData: fetchInfluencers,
    limit: ITEMS_PER_PAGE,
    cacheKey: CACHE_KEY,
    dependencies: [filters], // Reset kada se filteri promene
  });

  return (
    <DashboardLayout requiredUserType="business">
      <TooltipProvider>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">
              Marketplace Influencera
            </h1>
            <p className="text-muted-foreground mt-1">
              Pronađite savršenog influencera za vašu kampanju
            </p>
          </div>

          {/* Error state */}
          {error && !isLoading ? (
            <Card className="border-destructive">
              <CardContent className="p-6">
                <p className="text-destructive">
                  Greška pri učitavanju influencera. Pokušajte ponovo.
                </p>
                <button
                  onClick={actions.refresh}
                  className="mt-2 text-sm text-primary hover:underline"
                >
                  Pokušaj ponovo
                </button>
              </CardContent>
            </Card>
          ) : null}

          {/* Infinite Scroll Container */}
          <div className="min-h-screen">
            <InfiniteScroll
              data={influencers}
              hasMore={hasMore}
              isLoading={isLoading}
              isLoadingMore={isLoadingMore}
              loadMore={actions.loadMore}
              cacheKey={CACHE_KEY}
              threshold={300} // Load more kada je 300px do dna
              className="space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                {influencers.map((influencer, index) => (
                  <InfluencerCard
                    key={`${influencer.id}-${index}`}
                    influencer={influencer}
                  />
                ))}
              </div>
            </InfiniteScroll>
          </div>

          {/* Empty state */}
          {!isLoading && influencers.length === 0 && !error && (
            <Card>
              <CardContent className="p-12 text-center">
                <h3 className="text-lg font-semibold mb-2">Nema influencera</h3>
                <p className="text-muted-foreground mb-4">
                  Trenutno nema dostupnih influencera na platformi.
                </p>
                <button
                  onClick={actions.refresh}
                  className="text-primary hover:underline"
                >
                  Osvježi stranicu
                </button>
              </CardContent>
            </Card>
          )}
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}
